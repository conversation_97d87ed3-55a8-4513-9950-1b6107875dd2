const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Check both databases
const databases = [
  { name: 'procurement.db', path: './procurement.db' },
  { name: 'working-procurement.db', path: './working-procurement.db' }
];

async function checkDatabase(dbInfo) {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbInfo.path, (err) => {
      if (err) {
        console.log(`❌ Cannot open ${dbInfo.name}: ${err.message}`);
        resolve(null);
        return;
      }
      
      console.log(`\n📊 Checking ${dbInfo.name}:`);
      
      // Check if purchase_requests table exists
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_requests'", (err, row) => {
        if (err) {
          console.log(`❌ Error checking table: ${err.message}`);
          db.close();
          resolve(null);
          return;
        }
        
        if (!row) {
          console.log(`❌ No purchase_requests table found`);
          db.close();
          resolve(null);
          return;
        }
        
        // Get all requests
        db.all('SELECT id, request_number, status, item_name, user_id, created_at FROM purchase_requests ORDER BY created_at DESC LIMIT 10', (err, rows) => {
          if (err) {
            console.log(`❌ Error fetching requests: ${err.message}`);
          } else {
            console.log(`✅ Found ${rows.length} requests:`);
            rows.forEach(r => {
              console.log(`  ID: ${r.id}, Status: ${r.status}, Item: ${r.item_name}, User: ${r.user_id}`);
            });
            
            // Get status counts
            db.all('SELECT status, COUNT(*) as count FROM purchase_requests GROUP BY status', (err, statusRows) => {
              if (!err && statusRows.length > 0) {
                console.log(`📈 Status breakdown:`);
                statusRows.forEach(s => {
                  console.log(`  ${s.status}: ${s.count}`);
                });
              }
            });
          }
          
          db.close();
          resolve(rows);
        });
      });
    });
  });
}

async function main() {
  console.log('🔍 Checking procurement databases...\n');
  
  for (const dbInfo of databases) {
    await checkDatabase(dbInfo);
  }
  
  console.log('\n✅ Database check complete!');
}

main().catch(console.error);
