const axios = require('axios');

const API_BASE = 'http://localhost:5000';

async function testWorkflow() {
  console.log('🧪 Testing Complete Procurement Workflow\n');

  try {
    // Test 1: Check finance pending requests
    console.log('1️⃣ Testing Finance Pending Requests Endpoint...');
    const financeResponse = await axios.get(`${API_BASE}/api/procurement-requests/finance-pending`);
    const pendingRequests = financeResponse.data.requests || [];
    console.log(`✅ Found ${pendingRequests.length} requests pending finance approval:`);
    pendingRequests.forEach(req => {
      console.log(`   - ID: ${req.id}, Item: ${req.item_name}, Status: ${req.status}, Requester: ${req.requester_name}`);
    });

    // Test 2: Check all finance requests
    console.log('\n2️⃣ Testing Finance All Requests Endpoint...');
    const allFinanceResponse = await axios.get(`${API_BASE}/api/procurement-requests/finance-all`);
    const allRequests = allFinanceResponse.data.requests || [];
    console.log(`✅ Found ${allRequests.length} total finance-related requests:`);
    allRequests.forEach(req => {
      console.log(`   - ID: ${req.id}, Item: ${req.item_name}, Status: ${req.status}`);
    });

    // Test 3: Test team leader approval (simulate)
    if (pendingRequests.length === 0) {
      console.log('\n3️⃣ No pending finance requests found. Let\'s check team leader pending requests...');
      
      try {
        const teamLeaderResponse = await axios.get(`${API_BASE}/api/procurement-requests/team-leader-pending?department=IT`);
        const teamLeaderRequests = teamLeaderResponse.data.requests || [];
        console.log(`✅ Found ${teamLeaderRequests.length} requests pending team leader approval in IT department:`);
        teamLeaderRequests.forEach(req => {
          console.log(`   - ID: ${req.id}, Item: ${req.item_name}, Status: ${req.status}`);
        });

        if (teamLeaderRequests.length > 0) {
          const testRequest = teamLeaderRequests[0];
          console.log(`\n🔄 Testing team leader approval for request ID: ${testRequest.id}`);
          
          // Simulate team leader approval
          const approvalResponse = await axios.put(`${API_BASE}/api/procurement-requests/${testRequest.id}/team-leader-action`, {
            action: 'approve',
            comments: 'Test approval - workflow verification',
            userId: 453 // Using a test user ID
          });
          
          console.log(`✅ Team leader approval result:`, approvalResponse.data);
          
          // Check if it moved to finance
          const updatedFinanceResponse = await axios.get(`${API_BASE}/api/procurement-requests/finance-pending`);
          const updatedPendingRequests = updatedFinanceResponse.data.requests || [];
          console.log(`✅ After approval, ${updatedPendingRequests.length} requests now pending finance approval`);
        }
      } catch (error) {
        console.log(`❌ Error testing team leader workflow: ${error.message}`);
      }
    }

    // Test 4: Test finance approval (if we have pending requests)
    const finalFinanceCheck = await axios.get(`${API_BASE}/api/procurement-requests/finance-pending`);
    const finalPendingRequests = finalFinanceCheck.data.requests || [];
    
    if (finalPendingRequests.length > 0) {
      console.log('\n4️⃣ Testing Finance Approval...');
      const testRequest = finalPendingRequests[0];
      console.log(`🔄 Testing finance approval for request ID: ${testRequest.id}`);
      
      try {
        const financeApprovalResponse = await axios.put(`${API_BASE}/api/procurement-requests/${testRequest.id}/finance-action`, {
          action: 'approve',
          comments: 'Test finance approval - workflow verification',
          userId: 454 // Using a test finance user ID
        });
        
        console.log(`✅ Finance approval result:`, financeApprovalResponse.data);
        
        // Check if it moved to procurement
        const procurementResponse = await axios.get(`${API_BASE}/api/procurement-requests/procurement-all`);
        const procurementRequests = procurementResponse.data.requests || [];
        console.log(`✅ After finance approval, ${procurementRequests.length} requests now in procurement queue`);
      } catch (error) {
        console.log(`❌ Error testing finance approval: ${error.message}`);
      }
    }

    console.log('\n✅ Workflow test completed!');

  } catch (error) {
    console.error('❌ Workflow test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testWorkflow();
