<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZCC Procurement System - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .error-card {
            background: #ffe8e8;
            border: 1px solid #f44336;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .credentials {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #dcfce7;
            border: 1px solid #16a34a;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 ZCC Procurement System</h1>
            <h2>System Test & Status Page</h2>
        </div>

        <div class="status-card">
            <h3>✅ System Status</h3>
            <p><strong>API Server:</strong> http://localhost:5000</p>
            <p><strong>Client Server:</strong> http://localhost:3000</p>
            <p><strong>Database:</strong> SQLite (working-procurement.db)</p>
        </div>

        <div class="credentials">
            <h3>🔑 Test Login Credentials</h3>
            <p><strong>Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Employee:</strong> <EMAIL> / employee123</p>
            <p><strong>Team Leader:</strong> <EMAIL> / teamleader123</p>
            <p><strong>Finance:</strong> <EMAIL> / finance123</p>
            <p><strong>Procurement:</strong> <EMAIL> / procurement123</p>
        </div>

        <div>
            <h3>🧪 Quick Tests</h3>
            <button class="test-button" onclick="testAPIHealth()">Test API Health</button>
            <button class="test-button" onclick="testLogin()">Test Login</button>
            <button class="test-button" onclick="openMainApp()">Open Main Application</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function testAPIHealth() {
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                addResult(`✅ API Health Check: ${data.status} - Database: ${data.database}`, true);
            } catch (error) {
                addResult(`❌ API Health Check Failed: ${error.message}`, false);
            }
        }

        async function testLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Login Test: Success - User: ${data.user.username} (${data.user.role})`, true);
                } else {
                    const error = await response.json();
                    addResult(`❌ Login Test Failed: ${error.message}`, false);
                }
            } catch (error) {
                addResult(`❌ Login Test Error: ${error.message}`, false);
            }
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }

        // Auto-run health check on page load
        window.onload = function() {
            testAPIHealth();
        };
    </script>
</body>
</html>
